# Trade Manage Frontend 实战示例

## 完整功能模块开发示例：商品管理

本示例展示如何从零开始在 Trade Manage Frontend 中开发一个完整的商品管理功能模块，包括列表页、详情页、新增/编辑表单等。

### 1. 项目结构规划

```
src/app/(main)/dashboard/products/
├── page.tsx                           # 商品列表主页
├── loading.tsx                        # 加载状态
├── error.tsx                          # 错误状态
├── create/
│   └── page.tsx                       # 新增商品页面
├── [id]/
│   ├── page.tsx                       # 商品详情页
│   └── edit/
│       └── page.tsx                   # 编辑商品页面
├── _components/
│   ├── product-schema.ts              # 数据模型
│   ├── product-columns.tsx            # 表格列定义
│   ├── product-filters.tsx            # 筛选组件
│   ├── product-form.tsx               # 表单组件
│   ├── product-actions.tsx            # 操作按钮
│   └── product-stats.tsx              # 统计卡片
└── _lib/
    └── product-api.ts                 # API 调用封装
```

### 2. 数据模型定义

```typescript
// src/app/(main)/dashboard/products/_components/product-schema.ts
import { z } from "zod";

// 基础商品模型
export const productSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "商品名称不能为空").max(100, "商品名称不能超过100字符"),
  description: z.string().optional(),
  category: z.string().min(1, "请选择商品分类"),
  brand: z.string().optional(),
  sku: z.string().min(1, "SKU不能为空"),
  price: z.number().positive("价格必须大于0"),
  costPrice: z.number().positive("成本价必须大于0").optional(),
  stock: z.number().int().min(0, "库存不能为负数"),
  minStock: z.number().int().min(0, "最低库存不能为负数").optional(),
  status: z.enum(["active", "inactive", "outOfStock"]),
  tags: z.array(z.string()).optional(),
  images: z.array(z.string()).optional(),
  attributes: z.record(z.string()).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.string(),
});

// 创建商品表单模型
export const createProductSchema = productSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  createdBy: true,
});

// 更新商品表单模型
export const updateProductSchema = createProductSchema.partial();

// 筛选参数模型
export const productFiltersSchema = z.object({
  search: z.string().optional(),
  category: z.string().optional(),
  brand: z.string().optional(),
  status: z.enum(["active", "inactive", "outOfStock"]).optional(),
  minPrice: z.number().optional(),
  maxPrice: z.number().optional(),
  tags: z.array(z.string()).optional(),
});

// TypeScript 类型
export type Product = z.infer<typeof productSchema>;
export type CreateProductData = z.infer<typeof createProductSchema>;
export type UpdateProductData = z.infer<typeof updateProductSchema>;
export type ProductFilters = z.infer<typeof productFiltersSchema>;

// 商品状态选项
export const productStatusOptions = [
  { value: "active", label: "启用", color: "green" },
  { value: "inactive", label: "禁用", color: "gray" },
  { value: "outOfStock", label: "缺货", color: "red" },
] as const;

// 商品分类选项（实际项目中应该从API获取）
export const productCategoryOptions = [
  { value: "electronics", label: "电子产品" },
  { value: "clothing", label: "服装" },
  { value: "books", label: "图书" },
  { value: "home", label: "家居用品" },
  { value: "sports", label: "体育用品" },
] as const;
```

### 3. API 封装

```typescript
// src/app/(main)/dashboard/products/_lib/product-api.ts
import { lambdaClient } from "@/lib/lambda-client";
import type { Product, CreateProductData, UpdateProductData, ProductFilters } from "../_components/product-schema";

export interface ProductListResponse {
  data: Product[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

export class ProductAPI {
  private basePath = "/prod/products";

  // 获取商品列表
  async getProducts(filters: ProductFilters = {}, pagination: PaginationParams = {}): Promise<ProductListResponse> {
    const params = new URLSearchParams();

    // 添加筛选参数
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        if (Array.isArray(value)) {
          value.forEach((v) => params.append(key, v));
        } else {
          params.append(key, String(value));
        }
      }
    });

    // 添加分页参数
    if (pagination.page) params.append("page", String(pagination.page));
    if (pagination.pageSize) params.append("pageSize", String(pagination.pageSize));

    const queryString = params.toString();
    const url = queryString ? `${this.basePath}?${queryString}` : this.basePath;

    return lambdaClient.get<ProductListResponse>(url);
  }

  // 获取单个商品
  async getProduct(id: string): Promise<Product> {
    return lambdaClient.get<Product>(`${this.basePath}/${id}`);
  }

  // 创建商品
  async createProduct(data: CreateProductData): Promise<Product> {
    return lambdaClient.post<Product>(this.basePath, data);
  }

  // 更新商品
  async updateProduct(id: string, data: UpdateProductData): Promise<Product> {
    return lambdaClient.put<Product>(`${this.basePath}/${id}`, data);
  }

  // 删除商品
  async deleteProduct(id: string): Promise<void> {
    return lambdaClient.delete<void>(`${this.basePath}/${id}`);
  }

  // 批量删除商品
  async deleteProducts(ids: string[]): Promise<void> {
    return lambdaClient.delete<void>(`${this.basePath}/batch`, {
      headers: { "Content-Type": "application/json" },
      body: { ids },
    });
  }

  // 获取商品统计
  async getProductStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    outOfStock: number;
    lowStock: number;
  }> {
    return lambdaClient.get<any>(`${this.basePath}/stats`);
  }

  // 导出商品数据
  async exportProducts(filters: ProductFilters = {}): Promise<Blob> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        params.append(key, String(value));
      }
    });

    const response = await fetch(`/api/products/export?${params.toString()}`, {
      headers: {
        Accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      },
    });

    if (!response.ok) {
      throw new Error("导出失败");
    }

    return response.blob();
  }
}

export const productAPI = new ProductAPI();
```

### 4. 表格列定义

```typescript
// src/app/(main)/dashboard/products/_components/product-columns.tsx
'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { Product, productStatusOptions } from './product-schema';

interface ProductActionsProps {
  product: Product;
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
}

function ProductActions({ product, onEdit, onDelete }: ProductActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">打开菜单</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem asChild>
          <Link href={`/dashboard/products/${product.id}`}>
            <Eye className="mr-2 h-4 w-4" />
            查看详情
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onEdit(product)}>
          <Edit className="mr-2 h-4 w-4" />
          编辑
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => onDelete(product)}
          className="text-destructive"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          删除
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const createProductColumns = (
  onEdit: (product: Product) => void,
  onDelete: (product: Product) => void
): ColumnDef<Product>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="全选"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="选择行"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'images',
    header: '图片',
    cell: ({ row }) => {
      const images = row.original.images || [];
      const firstImage = images[0];

      return (
        <Avatar className="h-10 w-10 rounded-md">
          {firstImage ? (
            <AvatarImage src={firstImage} alt={row.original.name} />
          ) : null}
          <AvatarFallback className="rounded-md">
            {row.original.name.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="商品名称" />
    ),
    cell: ({ row }) => {
      return (
        <div className="max-w-[200px]">
          <div className="font-medium truncate">{row.original.name}</div>
          <div className="text-sm text-muted-foreground truncate">
            SKU: {row.original.sku}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'category',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="分类" />
    ),
    cell: ({ row }) => {
      const categoryLabels = {
        electronics: '电子产品',
        clothing: '服装',
        books: '图书',
        home: '家居用品',
        sports: '体育用品',
      };

      return (
        <Badge variant="outline">
          {categoryLabels[row.original.category as keyof typeof categoryLabels] || row.original.category}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'price',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="价格" />
    ),
    cell: ({ row }) => {
      const price = parseFloat(row.getValue('price'));
      const formatted = new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
      }).format(price);

      return <div className="font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: 'stock',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="库存" />
    ),
    cell: ({ row }) => {
      const stock = row.getValue('stock') as number;
      const minStock = row.original.minStock || 0;
      const isLowStock = stock <= minStock;

      return (
        <div className={`font-medium ${isLowStock ? 'text-red-600' : ''}`}>
          {stock}
          {isLowStock && <span className="text-xs ml-1">(低库存)</span>}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="状态" />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      const statusOption = productStatusOptions.find(option => option.value === status);

      if (!statusOption) return null;

      const colorVariants = {
        green: 'bg-green-100 text-green-800 hover:bg-green-200',
        gray: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
        red: 'bg-red-100 text-red-800 hover:bg-red-200',
      };

      return (
        <Badge className={colorVariants[statusOption.color]}>
          {statusOption.label}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'updatedAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="更新时间" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('updatedAt'));
      return (
        <div className="text-sm">
          {date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
          })}
        </div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <ProductActions
        product={row.original}
        onEdit={onEdit}
        onDelete={onDelete}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
];
```

### 5. 筛选组件

```typescript
// src/app/(main)/dashboard/products/_components/product-filters.tsx
'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useCallback, useEffect } from 'react';
import { Search, X, Filter, Download } from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

import { ProductFilters as FilterType, productCategoryOptions, productStatusOptions } from './product-schema';
import { productAPI } from '../_lib/product-api';

interface ProductFiltersProps {
  onExport?: () => void;
}

export function ProductFilters({ onExport }: ProductFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<FilterType>({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    status: searchParams.get('status') as any || '',
    minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
    maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
  });

  const [priceRange, setPriceRange] = useState({
    min: filters.minPrice?.toString() || '',
    max: filters.maxPrice?.toString() || '',
  });

  // 更新URL参数
  const updateFilters = useCallback((newFilters: FilterType) => {
    const params = new URLSearchParams(searchParams);

    // 重置页码
    params.delete('page');

    // 更新筛选参数
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.set(key, String(value));
      } else {
        params.delete(key);
      }
    });

    router.push(`?${params.toString()}`);
  }, [router, searchParams]);

  // 应用筛选
  const handleApplyFilters = () => {
    const newFilters = {
      ...filters,
      minPrice: priceRange.min ? Number(priceRange.min) : undefined,
      maxPrice: priceRange.max ? Number(priceRange.max) : undefined,
    };
    setFilters(newFilters);
    updateFilters(newFilters);
  };

  // 重置筛选
  const handleReset = () => {
    const resetFilters: FilterType = {
      search: '',
      category: '',
      status: '',
      minPrice: undefined,
      maxPrice: undefined,
    };
    setFilters(resetFilters);
    setPriceRange({ min: '', max: '' });
    updateFilters(resetFilters);
  };

  // 快速搜索
  const handleQuickSearch = (value: string) => {
    const newFilters = { ...filters, search: value };
    setFilters(newFilters);
    updateFilters(newFilters);
  };

  // 移除单个筛选条件
  const removeFilter = (key: keyof FilterType) => {
    const newFilters = { ...filters, [key]: '' };
    setFilters(newFilters);
    updateFilters(newFilters);
  };

  // 获取当前活跃的筛选条件
  const getActiveFilters = () => {
    const active: Array<{ key: keyof FilterType; label: string; value: string }> = [];

    if (filters.search) {
      active.push({ key: 'search', label: '搜索', value: filters.search });
    }
    if (filters.category) {
      const categoryLabel = productCategoryOptions.find(opt => opt.value === filters.category)?.label;
      active.push({ key: 'category', label: '分类', value: categoryLabel || filters.category });
    }
    if (filters.status) {
      const statusLabel = productStatusOptions.find(opt => opt.value === filters.status)?.label;
      active.push({ key: 'status', label: '状态', value: statusLabel || filters.status });
    }
    if (filters.minPrice || filters.maxPrice) {
      const priceLabel = `¥${filters.minPrice || 0} - ¥${filters.maxPrice || '∞'}`;
      active.push({ key: 'minPrice', label: '价格范围', value: priceLabel });
    }

    return active;
  };

  const activeFilters = getActiveFilters();

  return (
    <div className="space-y-4">
      {/* 搜索和快速筛选 */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex-1 min-w-[300px]">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索商品名称或SKU..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleQuickSearch(filters.search);
                }
              }}
              className="pl-10"
            />
          </div>
        </div>

        <Select
          value={filters.category}
          onValueChange={(value) => {
            const newFilters = { ...filters, category: value === 'all' ? '' : value };
            setFilters(newFilters);
            updateFilters(newFilters);
          }}
        >
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="分类" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部分类</SelectItem>
            {productCategoryOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={filters.status}
          onValueChange={(value) => {
            const newFilters = { ...filters, status: value === 'all' ? '' : value as any };
            setFilters(newFilters);
            updateFilters(newFilters);
          }}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            {productStatusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 高级筛选 */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              高级筛选
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div>
                <Label htmlFor="price-range">价格范围</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    id="price-min"
                    placeholder="最低价"
                    type="number"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                  />
                  <span className="flex items-center text-muted-foreground">-</span>
                  <Input
                    id="price-max"
                    placeholder="最高价"
                    type="number"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                  />
                </div>
              </div>

              <Separator />

              <div className="flex gap-2">
                <Button onClick={handleApplyFilters} className="flex-1">
                  应用筛选
                </Button>
                <Button variant="outline" onClick={handleReset}>
                  重置
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* 导出按钮 */}
        {onExport && (
          <Button variant="outline" onClick={onExport}>
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        )}
      </div>

      {/* 活跃筛选条件 */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-muted-foreground">筛选条件：</span>
          {activeFilters.map((filter) => (
            <Badge key={filter.key} variant="secondary" className="gap-1">
              <span className="text-xs">{filter.label}:</span>
              <span>{filter.value}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1"
                onClick={() => removeFilter(filter.key)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          <Button variant="ghost" size="sm" onClick={handleReset}>
            清空全部
          </Button>
        </div>
      )}
    </div>
  );
}
```

### 6. 商品表单组件

```typescript
// src/app/(main)/dashboard/products/_components/product-form.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Upload, X, Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import {
  createProductSchema,
  updateProductSchema,
  productCategoryOptions,
  productStatusOptions,
  type CreateProductData,
  type UpdateProductData,
  type Product,
} from './product-schema';

interface ProductFormProps {
  product?: Product;
  onSubmit: (data: CreateProductData | UpdateProductData) => Promise<void>;
  onCancel: () => void;
}

export function ProductForm({ product, onSubmit, onCancel }: ProductFormProps) {
  const [loading, setLoading] = useState(false);
  const [images, setImages] = useState<string[]>(product?.images || []);
  const [tags, setTags] = useState<string[]>(product?.tags || []);
  const [newTag, setNewTag] = useState('');

  const isEditing = !!product;
  const schema = isEditing ? updateProductSchema : createProductSchema;

  const form = useForm<CreateProductData | UpdateProductData>({
    resolver: zodResolver(schema),
    defaultValues: product ? {
      name: product.name,
      description: product.description,
      category: product.category,
      brand: product.brand,
      sku: product.sku,
      price: product.price,
      costPrice: product.costPrice,
      stock: product.stock,
      minStock: product.minStock,
      status: product.status,
    } : {
      name: '',
      description: '',
      category: '',
      brand: '',
      sku: '',
      price: 0,
      costPrice: 0,
      stock: 0,
      minStock: 0,
      status: 'active',
    },
  });

  const handleSubmit = async (data: CreateProductData | UpdateProductData) => {
    setLoading(true);
    try {
      await onSubmit({
        ...data,
        images,
        tags,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    // 这里应该实现真实的图片上传逻辑
    // 为了演示，我们假设上传成功并返回URL
    const newImageUrls = Array.from(files).map((file, index) =>
      `https://example.com/images/${Date.now()}-${index}.jpg`
    );

    setImages(prev => [...prev, ...newImageUrls]);
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags(prev => [...prev, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setTags(prev => prev.filter(t => t !== tag));
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 基本信息 */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>商品名称 *</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入商品名称" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="sku"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SKU *</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入SKU" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>商品描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请输入商品描述"
                          className="resize-none"
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>商品分类 *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择分类" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {productCategoryOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="brand"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>品牌</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入品牌" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 定价和库存 */}
            <Card>
              <CardHeader>
                <CardTitle>定价和库存</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>销售价格 *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="costPrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>成本价格</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="stock"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>当前库存 *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="minStock"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>最低库存</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          库存低于此值时会显示库存不足警告
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 商品状态 */}
            <Card>
              <CardHeader>
                <CardTitle>商品状态</CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>状态</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {productStatusOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* 商品图片 */}
            <Card>
              <CardHeader>
                <CardTitle>商品图片</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  {images.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`商品图片 ${index + 1}`}
                        className="w-full h-20 object-cover rounded border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => removeImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>

                <div>
                  <Label htmlFor="image-upload" className="cursor-pointer">
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center hover:border-muted-foreground/50 transition-colors">
                      <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">
                        点击上传图片
                      </p>
                    </div>
                  </Label>
                  <Input
                    id="image-upload"
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageUpload}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 标签 */}
            <Card>
              <CardHeader>
                <CardTitle>商品标签</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="gap-1">
                      {tag}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 ml-1"
                        onClick={() => removeTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Input
                    placeholder="添加标签"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addTag();
                      }
                    }}
                  />
                  <Button type="button" variant="outline" onClick={addTag}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? '保存中...' : isEditing ? '更新商品' : '创建商品'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
```

### 7. 商品列表主页面

```typescript
// src/app/(main)/dashboard/products/page.tsx
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/data-table/data-table';
import { DataTablePagination } from '@/components/data-table/data-table-pagination';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { useDataTableInstance } from '@/hooks/use-data-table-instance';
import { ProductFilters } from './_components/product-filters';
import { createProductColumns } from './_components/product-columns';
import { productAPI } from './_lib/product-api';
import type { Product, ProductFilters as FilterType } from './_components/product-schema';

export default function ProductsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [data, setData] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [batchDeleteDialogOpen, setBatchDeleteDialogOpen] = useState(false);

  // 解析查询参数
  const page = Number(searchParams.get('page')) || 1;
  const pageSize = Number(searchParams.get('pageSize')) || 10;
  const filters: FilterType = {
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    status: searchParams.get('status') as any || '',
    minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
    maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
  };

  // 编辑和删除处理函数
  const handleEdit = useCallback((product: Product) => {
    router.push(`/dashboard/products/${product.id}/edit`);
  }, [router]);

  const handleDelete = useCallback((product: Product) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
  }, []);

  // 初始化数据表格
  const table = useDataTableInstance({
    data,
    columns: createProductColumns(handleEdit, handleDelete),
    defaultPageIndex: page - 1,
    defaultPageSize: pageSize,
    enableRowSelection: true,
  });

  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const result = await productAPI.getProducts(filters, { page, pageSize });
      setData(result.data);
      setTotalCount(result.total);
    } catch (error) {
      console.error('Failed to fetch products:', error);
      toast.error('获取商品列表失败');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, filters]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 删除商品
  const confirmDelete = async () => {
    if (!productToDelete) return;

    try {
      await productAPI.deleteProduct(productToDelete.id);
      toast.success('商品删除成功');
      setDeleteDialogOpen(false);
      setProductToDelete(null);
      fetchData();
    } catch (error) {
      console.error('Failed to delete product:', error);
      toast.error('删除商品失败');
    }
  };

  // 批量删除
  const handleBatchDelete = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    if (selectedRows.length === 0) {
      toast.error('请先选择要删除的商品');
      return;
    }
    setBatchDeleteDialogOpen(true);
  };

  const confirmBatchDelete = async () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const ids = selectedRows.map(row => row.original.id);

    try {
      await productAPI.deleteProducts(ids);
      toast.success(`成功删除 ${ids.length} 个商品`);
      setBatchDeleteDialogOpen(false);
      table.resetRowSelection();
      fetchData();
    } catch (error) {
      console.error('Failed to batch delete products:', error);
      toast.error('批量删除失败');
    }
  };

  // 导出数据
  const handleExport = async () => {
    try {
      const blob = await productAPI.exportProducts(filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `products-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export products:', error);
      toast.error('导出失败');
    }
  };

  const selectedRowCount = table.getFilteredSelectedRowModel().rows.length;

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">商品管理</h1>
          <p className="text-muted-foreground">管理您的商品库存和信息</p>
        </div>
        <div className="flex gap-2">
          {selectedRowCount > 0 && (
            <Button variant="destructive" onClick={handleBatchDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              删除选中 ({selectedRowCount})
            </Button>
          )}
          <Button onClick={() => router.push('/dashboard/products/create')}>
            <Plus className="mr-2 h-4 w-4" />
            添加商品
          </Button>
        </div>
      </div>

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <CardTitle>商品列表 ({totalCount})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <ProductFilters onExport={handleExport} />
            <DataTable table={table} columns={createProductColumns(handleEdit, handleDelete)} />
            <DataTablePagination table={table} totalCount={totalCount} />
          </div>
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除商品 "{productToDelete?.name}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量删除确认对话框 */}
      <AlertDialog open={batchDeleteDialogOpen} onOpenChange={setBatchDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认批量删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除选中的 {selectedRowCount} 个商品吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmBatchDelete}>删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
```

### 8. 对应的 API 路由实现

```typescript
// src/app/api/products/route.ts
import { NextRequest, NextResponse } from "next/server";
import { lambdaClient } from "@/lib/lambda-client";
import { productFiltersSchema } from "@/app/(main)/dashboard/products/_components/product-schema";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 解析和验证查询参数
    const filters = productFiltersSchema.safeParse({
      search: searchParams.get("search") || undefined,
      category: searchParams.get("category") || undefined,
      status: searchParams.get("status") || undefined,
      minPrice: searchParams.get("minPrice") ? Number(searchParams.get("minPrice")) : undefined,
      maxPrice: searchParams.get("maxPrice") ? Number(searchParams.get("maxPrice")) : undefined,
    });

    if (!filters.success) {
      return NextResponse.json({ error: "无效的查询参数", details: filters.error.errors }, { status: 400 });
    }

    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;

    // 构建查询参数
    const params = new URLSearchParams();
    params.append("page", page.toString());
    params.append("pageSize", pageSize.toString());

    Object.entries(filters.data).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, String(value));
      }
    });

    // 调用 Lambda API
    const lambdaUrl = `/prod/products?${params.toString()}`;
    const result = await lambdaClient.get(lambdaUrl);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Products API error:", error);
    return NextResponse.json({ error: "获取商品列表失败" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 这里可以添加数据验证
    // const validatedData = createProductSchema.parse(body);

    const result = await lambdaClient.post("/prod/products", body);

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Create product error:", error);
    return NextResponse.json({ error: "创建商品失败" }, { status: 500 });
  }
}
```

这个完整的实战示例展示了如何在 Trade Manage Frontend 中开发一个功能完整的商品管理模块，包括：

1. **完整的 CRUD 操作**
2. **高级筛选和搜索**
3. **数据验证和错误处理**
4. **批量操作支持**
5. **文件上传和图片管理**
6. **与 AWS Lambda 的 API 集成**
7. **响应式设计和良好的用户体验**

你可以基于这个示例来开发其他功能模块，如用户管理、订单管理等。
