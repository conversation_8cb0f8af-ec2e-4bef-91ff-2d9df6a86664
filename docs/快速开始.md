# Trade Manage Frontend 快速开始

## 项目简介

Trade Manage Frontend 是一个基于 Next.js 15 + TypeScript + Shadcn/ui 构建的现代化管理后台模板。这个项目采用 **Colocation 架构**，为你提供了一个完整的企业级管理系统基础框架。

### 🚀 核心特性

- ✅ **Next.js 15 App Router** - 最新的 React 框架和路由系统
- ✅ **TypeScript** - 全栈类型安全
- ✅ **Shadcn/ui** - 现代化组件库
- ✅ **TanStack Table** - 强大的数据表格
- ✅ **Zustand** - 轻量级状态管理
- ✅ **多主题支持** - 明暗模式 + 多种颜色主题
- ✅ **响应式设计** - 移动端友好
- ✅ **认证系统** - 完整的用户认证框架

## 环境要求

- **Node.js**: >= 18.0.0
- **包管理器**: npm (>= 8.0.0) 或 yarn (>= 1.22.0)
- **操作系统**: macOS, Linux, Windows (推荐 WSL)

## 安装和启动

### 1. 克隆项目

```bash
git clone <your-repository-url>
cd trade-manage-frontend
```

### 2. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 3. 环境配置

创建环境变量文件：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件：

```bash
# AWS Lambda 配置
AWS_LAMBDA_BASE_URL=https://your-api-gateway-url.amazonaws.com
AWS_API_KEY=your-api-key
AWS_REGION=us-east-1

# 应用配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 项目结构概览

```
src/
├── app/                    # Next.js App Router 页面
│   ├── (main)/            # 主应用布局组
│   │   ├── dashboard/     # 仪表板功能模块
│   │   │   ├── layout.tsx # 仪表板布局
│   │   │   ├── page.tsx   # 默认仪表板
│   │   │   ├── crm/       # CRM 模块
│   │   │   ├── finance/   # 财务模块
│   │   │   └── [feature]/ # 其他功能模块
│   │   └── auth/          # 认证页面
│   └── api/               # API 路由
├── components/            # 全局共享组件
│   ├── ui/               # 原子级 UI 组件
│   └── data-table/       # 数据表格组件
├── hooks/                # 自定义 Hooks
├── lib/                  # 工具函数
├── stores/               # 状态管理
├── types/                # TypeScript 类型定义
└── navigation/           # 导航配置
```

## 核心概念

### 1. Colocation 架构

项目采用 **就近组织** 的架构模式，每个功能模块的相关文件都放在一起：

```
src/app/(main)/dashboard/products/
├── page.tsx                    # 主页面
├── _components/               # 模块专用组件
│   ├── product-list.tsx
│   ├── product-form.tsx
│   ├── columns.tsx
│   └── schema.ts
└── [id]/                      # 子路由
    └── page.tsx
```

### 2. 数据表格系统

基于 TanStack Table v8 构建的高级数据表格：

- ✅ 排序、筛选、分页
- ✅ 列可见性控制
- ✅ 拖拽排序
- ✅ 批量操作
- ✅ 响应式设计

### 3. 主题系统

支持多种主题和布局配置：

- **明暗模式**: light/dark 自动切换
- **颜色主题**: default/tangerine/brutalist/soft-pop
- **布局选项**: 侧边栏变体、内容宽度

### 4. 状态管理

- **Zustand**: 用户偏好设置
- **TanStack Query**: 服务端状态管理
- **React Hook Form**: 表单状态

## 5 分钟快速上手

### 第一步：查看现有功能

项目已经包含了几个示例仪表板：

1. **Default Dashboard** - `/dashboard/default`
   - 数据图表展示
   - 交互式数据表格
   - 统计卡片

2. **CRM Dashboard** - `/dashboard/crm`
   - 客户管理
   - 销售线索跟踪
   - 业务指标

3. **Finance Dashboard** - `/dashboard/finance`
   - 财务概览
   - 收支分析
   - 账户管理

### 第二步：添加你的第一个菜单

编辑 [`src/navigation/sidebar/sidebar-items.ts`](src/navigation/sidebar/sidebar-items.ts)：

```typescript
import { Package } from "lucide-react";

export const sidebarItems: NavGroup[] = [
  {
    id: 1,
    label: "主要功能",
    items: [
      // 添加新菜单项
      {
        title: "产品管理",
        url: "/dashboard/products",
        icon: Package,
      },
      // ... 其他菜单项
    ],
  },
];
```

### 第三步：创建对应页面

创建产品管理页面：

```bash
mkdir -p src/app/\(main\)/dashboard/products/_components
```

创建基础页面文件：

```typescript
// src/app/(main)/dashboard/products/page.tsx
export default function ProductsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">产品管理</h1>
        <p className="text-muted-foreground">管理您的产品信息</p>
      </div>

      <div className="border rounded-lg p-8 text-center">
        <h2 className="text-lg font-semibold mb-2">开始构建您的产品管理系统</h2>
        <p className="text-muted-foreground">
          参考 docs/开发指南.md 和 docs/实战示例.md 来构建完整功能
        </p>
      </div>
    </div>
  );
}
```

### 第四步：启用认证系统（可选）

如果需要用户认证功能：

1. **启用中间件**：

   ```bash
   mv src/middleware.disabled.ts src/middleware.ts
   ```

2. **配置认证逻辑**：
   根据你的后端 API 调整认证逻辑

3. **测试认证流程**：
   访问 `/auth/v1/login` 或 `/auth/v2/login`

## 常用开发命令

```bash
# 开发服务器
npm run dev

# 生产构建
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type-check

# 生成主题预设
npm run generate:presets
```

## 开发工作流

### 1. 创建新功能模块

```bash
# 创建功能目录
mkdir -p src/app/\(main\)/dashboard/orders/_components

# 创建基础文件
touch src/app/\(main\)/dashboard/orders/page.tsx
touch src/app/\(main\)/dashboard/orders/_components/schema.ts
touch src/app/\(main\)/dashboard/orders/_components/columns.tsx
```

### 2. 添加 API 路由

```bash
# 创建 API 路由
mkdir -p src/app/api/orders
touch src/app/api/orders/route.ts
```

### 3. 更新菜单配置

在 `sidebar-items.ts` 中添加新菜单项

### 4. 开发和测试

使用热重载进行快速开发和测试

## 接下来做什么？

### 📚 学习资源

- **开发指南**: [`docs/开发指南.md`](docs/开发指南.md) - 详细的开发文档
- **实战示例**: [`docs/实战示例.md`](docs/实战示例.md) - 完整的商品管理模块示例
- **Next.js 文档**: [nextjs.org](https://nextjs.org/docs)
- **Shadcn/ui 文档**: [ui.shadcn.com](https://ui.shadcn.com)

### 🛠️ 开发建议

1. **先看示例**: 查看现有的 CRM 和 Finance 模块了解项目结构
2. **从简单开始**: 创建基础的 CRUD 页面
3. **逐步完善**: 添加筛选、搜索、批量操作等高级功能
4. **参考文档**: 遇到问题时查看开发指南和实战示例

### 🎯 项目定制

- **主题配置**: 在 `src/styles/presets/` 添加自定义主题
- **组件扩展**: 在 `src/components/ui/` 添加自定义组件
- **API 集成**: 配置你的后端 API 或 AWS Lambda
- **国际化**: 添加多语言支持

## 故障排除

### 常见问题

**Q: 页面样式不显示**
A: 确认已正确导入 Tailwind CSS 样式，检查 `globals.css` 文件

**Q: 中间件不生效**
A: 确认文件名为 `middleware.ts` 而不是 `middleware.disabled.ts`

**Q: API 请求失败**
A: 检查环境变量配置，确认 AWS Lambda 端点可访问

**Q: 类型错误**
A: 运行 `npm run type-check` 检查 TypeScript 错误

### 获得帮助

- 查看项目 Issues
- 参考 Next.js 和 React 官方文档
- 检查浏览器开发者工具的 Console 和 Network 面板

---

🎉 **恭喜！** 你已经准备好开始使用 Trade Manage Frontend 进行开发了。

记住，这个项目模板为你提供了一个强大的基础，你可以根据具体业务需求进行定制和扩展。祝你开发愉快！
