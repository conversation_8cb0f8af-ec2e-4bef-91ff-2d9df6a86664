{"name": "trade-manage-backend", "version": "1.0.0", "description": "Trade Management Backend with NestJS and AWS CDK", "author": "Your Name", "private": true, "license": "MIT", "scripts": {"build": "nest build", "clean": "./scripts/clean.sh", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node build/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "node --test", "cdk:deploy:dev": "cd infrastructure && npm run cdk deploy -- --context environment=dev", "cdk:deploy:prod": "cd infrastructure && npm run cdk deploy -- --context environment=prod", "cdk:destroy:dev": "cd infrastructure && npm run cdk destroy -- --context environment=dev", "cdk:destroy:prod": "cd infrastructure && npm run cdk destroy -- --context environment=prod", "cdk:diff:dev": "cd infrastructure && npm run cdk diff -- --context environment=dev", "cdk:diff:prod": "cd infrastructure && npm run cdk diff -- --context environment=prod", "build:lambda": "./scripts/build-lambda.sh", "deploy:lambda:dev": "npm run build:lambda && npm run cdk:deploy:dev", "deploy:lambda:prod": "npm run build:lambda && npm run cdk:deploy:prod", "destroy:lambda:dev": "npm run cdk:destroy:dev", "destroy:lambda:prod": "npm run cdk:destroy:prod", "deploy:dev": "npm run build:lambda && npm run cdk:deploy:dev", "deploy:prod": "npm run build:lambda && npm run cdk:deploy:prod", "prepare": "if [ -d .git ]; then husky install; fi"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.490.0", "@aws-sdk/client-dynamodb": "^3.490.0", "@aws-sdk/client-s3": "^3.490.0", "@aws-sdk/lib-dynamodb": "^3.490.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@nestjs/common": "^11.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/swagger": "^8.0.0", "@types/aws-lambda": "^8.10.150", "@types/multer": "^2.0.0", "@vendia/serverless-express": "^4.12.6", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1540.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cors": "^2.8.5", "exceljs": "^4.4.0", "helmet": "^7.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.6", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "prettier": "^3.1.1", "source-map-support": "^0.5.21", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "webpack-cli": "^6.0.1", "husky": "^8.0.0"}}